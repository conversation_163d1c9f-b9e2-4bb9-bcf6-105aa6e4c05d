#ifndef __JYXX_H_
#define __JYXX_H_

/**
 * @brief       JYxx初始化
 * @param       无
 * @retval      无
 */
void jyxx_init(void);

/**
 * @brief       设置JYxx休眠或唤醒
 * @param       state: 休眠或唤醒   1:休眠   0:唤醒
 * @retval      无
 */
void jyxx_set_sleep_wake(uint8_t state);

/**
 * @brief       更新JYxx数据
 * @param       无
 * @retval      无
 */
void jyxx_up_data(void);

/**
 * @brief       获取JYxx加速度
 * @param       加速度
 * @retval
 */
float *jyxx_get_acceleration(void);

/**
 * @brief       获取JYxx角速度
 * @param       无
 * @retval      角速度
 */
float *jyxx_get_angle_speed(void);

/**
 * @brief       获取JYxx角度
 * @param       无
 * @retval      角度
 */
float *jyxx_get_angle(void);

/**
 * @brief       获取JYxx温度
 * @param       无
 * @retval      温度
 */
float jyxx_get_temperature(void);

/**
 * @brief       获取JYxx电压
 * @param       无
 * @retval      电压
 */
float jyxx_get_voltage(void);

/**
 * @brief       获取JYxx版本
 * @param       无
 * @retval      版本
 */
float jyxx_get_version(void);

/**
 * @brief       JYxx数据在OLED上显示
 * @param       无
 * @retval      无
 */
void jyxx_oled_show(void);

#endif
