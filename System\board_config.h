#ifndef __BOARD_CONFIG_H
#define __BOARD_CONFIG_H

// clang-format off

/*
**************************************************************************************************************************

           参考：          https://wiki.lckfb.com/zh-hans/tmx-mspm0g3507/keil-beginner/pwm.html                          

************************************************************************************************************************** 
*/

#include "ti_msp_dl_config.h"

#define CFG_ENABLE      1
#define CFG_DISABLE     0
#define CFG_DEFAULT     0 
#define CFG_NULL        0

/************************ LED状态指示灯 ************************/
//在工具中配置对应的LED名字 GPIO组名字：{LED}, PIN: {LEDX}
#define CFG_LED_EN                  CFG_ENABLE
#define CFG_LED_NUM                 4
#define CFG_LED_PORT                LED_LED1_PORT, LED_LED2_PORT, LED_LED3_PORT, LED_LED4_PORT
#define CFG_LED_PIN                 LED_LED1_PIN,LED_LED2_PIN , LED_LED3_PIN, LED_LED4_PIN  //状态指示灯引脚(对应下方顺序)
#define CFG_LED_POLARITY            0,  0, 0, 0                     //状态指示灯 亮灯电平(0:低电平 1:高电平)
#define CFG_LED_ON                  0                           //仅做宏使用，不作为配置
#define CFG_LED_OFF                 1 

/************************ 调试串口配置 ************************/
//在工具中配置对应的串口名字为UART_DEBUG  
#define CFG_DEBUG_EN                CFG_ENABLE
#define CFG_DEBUG_SYS_LOG           CFG_ENABLE
#define CFG_DEBUG_USMART_LOG        CFG_ENABLE   
#define CFG_DEBUG_MODE              1   //0:usb  1:uart  

/************************ 独立看门狗配置 ************************/
#define CFG_IWDG_EN                 CFG_DISABLE     //不支持独立看门狗

/************************ 窗口看门狗配置 ************************/
#define CFG_WWDG_EN                 CFG_DISABLE

/************************ GPIO按键 ************************/
//在工具中配置对应的GPIO组名字为KEY GPIO:{KEY}, PIN: {KEYx}
#define CFG_GPIO_KEY_EN             CFG_DISABLE
#define CFG_GPIO_KEY_PIN_NUM        5
#define CFG_GPIO_KEY_PORT           GPIO_KEY_KEY1_PORT, GPIO_KEY_KEY2_PORT, GPIO_KEY_KEY3_PORT, GPIO_KEY_KEY4_PORT, GPIO_KEY_KEY6_PORT                //按键引脚(对应下方顺序)
#define CFG_GPIO_KEY_PIN            GPIO_KEY_KEY1_PIN,GPIO_KEY_KEY2_PIN,GPIO_KEY_KEY3_PIN, GPIO_KEY_KEY4_PIN,GPIO_KEY_KEY6_PIN,          //按键引脚(对应下方顺序)
#define CFG_GPIO_KEY_VALUE          1,2,3,4,6                      //按键值
#define CFG_GPIO_KEY_POLARITY       0,0,0,0,0                      //按键按下时的电平(0:低电平 1:高电平)

/************************ ADC 按键 ************************/
//在工具中配置对应的ADC1的通道x名字为ADC_KEY  ADC名字为ADC
#define CFG_ADC_KEY_EN              CFG_ENABLE
#define CFG_ADC_KEY_INDEX           ADC_ADCMEM_ADC_KEY     //需要修改与工具配置一致的ADC_ID
#define CFG_ADC_KEY_NUM             5
#define CFG_ADC_KEY_VALUE           1,2,3,4,5

#define CFG_ADC_KEY_VDDIO           (0xfffL) // 4095
#define CFG_ADC_KEY_VALUE_MAX       4095
#define CFG_ADC_KEY_R_UP            100     //上拉电阻 10K
#define CFG_ADC_KEY_R0              1       //0.1k
#define CFG_ADC_KEY_R1              10      //1.0k
#define CFG_ADC_KEY_R2              22     //2.2k
#define CFG_ADC_KEY_R3              36     //3.6k
#define CFG_ADC_KEY_R4              50     //5.0k

#define CFG_ADC_KEY_ADC_0           (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R0 / (CFG_ADC_KEY_R0 + CFG_ADC_KEY_R_UP)) // 0.1k      0
#define CFG_ADC_KEY_ADC_1           (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R1 / (CFG_ADC_KEY_R1 + CFG_ADC_KEY_R_UP))    // 1.0k      410
#define CFG_ADC_KEY_ADC_2           (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R2 / (CFG_ADC_KEY_R2 + CFG_ADC_KEY_R_UP))  // 2.2k      910
#define CFG_ADC_KEY_ADC_3           (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R3 / (CFG_ADC_KEY_R3 + CFG_ADC_KEY_R_UP))  // 3.6k      1410
#define CFG_ADC_KEY_ADC_4           (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R4 / (CFG_ADC_KEY_R4 + CFG_ADC_KEY_R_UP))  // 5.0k      1910

#define CFG_ADC_KEY_VOLTAGE0        ((CFG_ADC_KEY_ADC_0 + CFG_ADC_KEY_ADC_1) / 2) // 783
#define CFG_ADC_KEY_VOLTAGE1        ((CFG_ADC_KEY_ADC_1 + CFG_ADC_KEY_ADC_2) / 2) // 2012
#define CFG_ADC_KEY_VOLTAGE2        ((CFG_ADC_KEY_ADC_2 + CFG_ADC_KEY_ADC_3) / 2) // 2673
#define CFG_ADC_KEY_VOLTAGE3        ((CFG_ADC_KEY_ADC_3 + CFG_ADC_KEY_ADC_4) / 2) // 3492
#define CFG_ADC_KEY_VOLTAGE4        ((CFG_ADC_KEY_ADC_4 + CFG_ADC_KEY_VDDIO) / 2) // 4312

#define CFG_ADC_KEY_ADC_VALUE       CFG_ADC_KEY_VOLTAGE0, CFG_ADC_KEY_VOLTAGE1, CFG_ADC_KEY_VOLTAGE2, CFG_ADC_KEY_VOLTAGE3,CFG_ADC_KEY_VOLTAGE4

/************************ 硬件定时器中断扫描函数配置 ************************/
//在工具中配置对应的定时器名字为：TIMER_FUNS，定时器中断时间按照下方参数配置
#define CFG_TIMER_FUNS_EN           CFG_ENABLE
#define CFG_TIMER_FUNS_PERIOD       10              //定时器扫描间隔 单位：ms 
#define TIMX_CALLBACK_FUNS_NUM      5               //定时器扫描回调函数个数

/************************ ADC读取数据配置 ************************/
//需要填写对应的ADC通道名字 和 对应的DMA通道
#define CFG_ADC_EN                  CFG_ENABLE
#define CFG_ADC_NUM                 1                       //需要读取的ADC通道个数
#define CFG_ADC_READ_BUF            CFG_ADC_KEY_INDEX,      //所有用到的ADC通道
#define CFG_ADC_DMA_CH              DMA_CH0_CHAN_ID         //ADC 的 DMA通道

/************************ OLED配置 ************************/
#define CFG_OLED_EN                 CFG_ENABLE
#define CFG_OLED_MODE               0   //0:软件IIC 3:硬件SPI
#define CFG_OLED_SOFT_IIC_ID        0   //软件IIC ID
#define CFG_OLED_SOFT_IIC_DELAY     4   //软件IIC延时 单位：us

//在工具中配置对应的OLED的硬件SPI名字为：SPI_OLED   GPIO组名字：{OLED}, PIN: {CS, RES, DC}
#define CFG_OLED_SPI_INST           SPI_OLED_INST
#define CFG_OLED_SPI_PORT           GPIO_SPI_OLED_PICO_PORT, GPIO_SPI_OLED_SCLK_PORT,OLED_PORT, OLED_PORT, OLED_PORT        //port: sda, sck, cs, res, dc
#define CFG_OLED_SPI_PIN            GPIO_SPI_OLED_PICO_PIN,GPIO_SPI_OLED_SCLK_PIN,OLED_CS_PIN, OLED_RES_PIN, OLED_DC_PIN    //pin:  sda, sck, cs, res, dc

/************************ 电机配置 ************************/
//在工具中配置对应的定时器PWM名字为：PWM_MOTOR
#define CFG_MOTOR_EN                    CFG_DISABLE
#define CFG_MOTOR_NUM                   2
#define CFG_MOTOR_PWM_TIMER             PWM_MOTOR_A_INST, PWM_MOTOR_A_INST        
#define CFG_MOTOR_PWM_TIMER_CH          GPIO_PWM_MOTOR_A_C0_IDX, GPIO_PWM_MOTOR_A_C1_IDX 
#define CFG_MOTOR_DEAD_BAND             0, 0                    //死区时间 修正比较值
#define CFG_MOTOR_WHEEL_DIAMETER_CM     (6.5f *3.14159265f)     //轮子直径，单位cm
#define CFG_MOTOR_PPR                   (13 * 28)          // 编码器每转脉冲数 13线/相, 20倍减速比, 4倍频 

/************************ 电机驱动配置 ************************/
//在工具中配置对应的驱动GPIO名字为：PORT:{驱动} PIN:{驱动}_X
#define CFG_MOTOR_DRIVER_EN             CFG_ENABLE
#define CFG_MOTOR_DRIVER_MODULE         1  //0:不使用 1:tb6612 2:L298N  
#define CFG_MOTOR_DRIVER_PWM_TIMER      CFG_MOTOR_PWM_TIMER
#define CFG_MOTOR_DRIVER_PWM_TIMER_CH   CFG_MOTOR_PWM_TIMER_CH
#define CFG_MOTOR_DEIVER_CONTROL_PORT   TB6612_PORT, TB6612_PORT, TB6612_PORT, TB6612_PORT
#define CFG_MOTOR_DEIVER_CONTROL_PIN    TB6612_AIN2_PIN, TB6612_AIN1_PIN,TB6612_BIN1_PIN, TB6612_BIN2_PIN

#define CFG_MOTOR_DIRECTION_STOP        0       //电机停止
#define CFG_MOTOR_DIRECTION_FORWARD     1       //电机正转
#define CFG_MOTOR_DIRECTION_BACKWARD    2       //电机反转

/************************ 外部中断AB相位编码器配置 ************************/
//在工具中配置对应的GPIO名字为：PORT:{ENCODER} PIN:{Ax, Bx}   A相配置为外部中断下降沿触发，B相配置为输入模式，上拉
#define CFG_ENCODER_INT_EN                  CFG_ENABLE
#define CFG_ENCODER_INT_NUM                 2
#define CFG_ENCODER_A_INT_IRQN              ENCODER_INT_IRQN, ENCODER_INT_IRQN,    //中断的GPIO组，注意是中断对应的GPIO组
#define CFG_ENCODER_A_INT_IIDX              ENCODER_INT_IIDX, ENCODER_INT_IIDX, 
#define CFG_ENCODER_A_PORT                  ENCODER_PORT, ENCODER_PORT,
#define CFG_ENCODER_A_PIN                   ENCODER_A1_PIN, ENCODER_A2_PIN,
#define CFG_ENCODER_B_PORT                  ENCODER_PORT, ENCODER_PORT,
#define CFG_ENCODER_B_PIN                   ENCODER_B1_PIN, ENCODER_B2_PIN,

/************************ freertos配置 ************************/    
#define CFG_FREERTOS_EN                     CFG_ENABLE
#define CFG_FREERTOS_CPU_CLOCK_HZ           CPUCLK_FREQ     //CPU时钟频率
#define CFG_FREERTOS_TICK_RATE_HZ           1000            //系统时钟频率
#define CFG_FREERTOS_MINIMAL_STACK_SIZE     32              //任务最小堆栈大小
#define CFG_FREERTOS_TOTAL_HEAP_SIZE        (10 * 1024)      //系统总堆栈大小
#define CFG_FREERTOS_TMAX_TASK_NAME_LEN     24              //任务名字最大长度

/*其他freertos功能在FreeRTOS.h的250行左右可以设置*/
#define CFG_FREERTOS_MUTEXES_EN                     0   //互斥锁使能
#define CFG_FREERTOS_TIMERS_EN                      0   //定时器使能
#define CFG_FREERTOS_EVENT_GROUPS_EN                0   //事件组使能
#define CFG_FREERTOS_STATS_FORMATTING_FUNCTIONS_EN  1   //统计格式化函数使能

/************************ JYxx 姿态传感器 配置 ************************/
//在工具中配置对应的串口名字为：UART_JYXX
#define CFG_JYxx_EN                     CFG_ENABLE
#define CFG_JYxx_MODULE                 1   //0:不使用 1:JY61 2:JY901 
 
/************************ 感为 GW循迹模块 配置 ************************/
#define CFG_GW_TRACK_EN                 CFG_ENABLE
#define CFG_GW_TRACK_MODE               2 // 0:无  1：iic  2：gpio

#define CFG_GW_TRACK_SOFT_IIC_ID        1       //软件IIC ID
#define CFG_GW_TRACK_SOFT_IIC_DELAY     20      //软件IIC延时 单位：us

#define CFG_GW_TRACK_GPIO_NUM           8
#define CFG_GW_TRACK_GPIO_PORT          TRACK_A_PORT,TRACK_B_PORT, TRACK_C_PORT,TRACK_D_PORT,TRACK_E_PORT,TRACK_F_PORT,TRACK_G_PORT,TRACK_H_PORT,
#define CFG_GW_TRACK_GPIO_PIN           TRACK_A_PIN, TRACK_B_PIN,TRACK_C_PIN, TRACK_D_PIN, TRACK_E_PIN, TRACK_F_PIN, TRACK_G_PIN, TRACK_H_PIN, 

/************************ 软件IIC配置 ************************/
//在工具中配置对应的软件IIC的GPIO组名字为：SOFT_I2Cx  x为0, 1 , 2, .. ,x
#define CFG_SOFT_IIC_NUM            2                   //软件IIC个数,对应ID 0开始
#define CFG_SOFT_IIC_SDA_PORT       SOFT_I2C0_PORT,          
#define CFG_SOFT_IIC_SDA_PIN        SOFT_I2C0_SDA_PIN, 
#define CFG_SOFT_IIC_SCL_PORT       SOFT_I2C0_PORT, 
#define CFG_SOFT_IIC_SCL_PIN        SOFT_I2C0_SCL_PIN, 
#define CFG_SOFT_IIC_DELAY          CFG_OLED_SOFT_IIC_DELAY, 
#define CFG_SOFT_IIC_SDA_MUX        SOFT_I2C0_SDA_IOMUX,

#endif /* __BOARD_CONFIG_H */


/*

MIN3

KEY1：PA26      KEY2:PA27   KEY4:PA25      KEY3:PB24    KEY6：PA22

LED3:PB7    LED4:PB6

OLED_SDA  PB20   SCL:PB14

MOTOR1_PWMA   

ENCODER_A1  PB05    ENCODER_B1  PA8
ENCODER_A2  PB02    ENCODER_B2  PB16

PWMA  PB27    PWMB  PA29

TB6612 AIN2:PB26  AIN1:PB23  BIN1:PA12  BIN2:PA13

track  A:PA1  B:PA9 C:PB4 D:PB15
 B10 B11 A30 B21


*/