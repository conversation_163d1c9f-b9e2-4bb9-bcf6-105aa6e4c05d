<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\..\Output\Output\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\..\Output\Output\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Thu Jul 31 21:37:25 2025
<BR><P>
<H3>Maximum Stack Usage =        456 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
app_ui_task &rArr; OLED_Printf &rArr; vsprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from lto-llvm-95641d.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from lto-llvm-95641d.o(.text.HardFault_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from lto-llvm-95641d.o(.text.PendSV_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from lto-llvm-95641d.o(.text.SVC_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from lto-llvm-95641d.o(.text.SysTick_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from lto-llvm-95641d.o(.text.TIMG8_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from lto-llvm-95641d.o(.text.UART0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from lto-llvm-95641d.o(.text.UART3_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1e]">__main</a> from __main.o(!!!main) referenced from startup_mspm0g350x_uvision.o(.text)
 <LI><a href="#[22]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[2c]">_printf_fp_dec_real</a> from _printf_fp_dec.o(.text) referenced from printf1.o(x$fpl$printf1)
 <LI><a href="#[2d]">_printf_fp_hex_real</a> from _printf_fp_hex.o(.text) referenced from printf2.o(x$fpl$printf2)
 <LI><a href="#[21]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[20]">_snputc</a> from _snputc.o(.text) referenced 2 times from vsnprintf.o(.text)
 <LI><a href="#[1f]">_sputc</a> from _sputc.o(.text) referenced from vsprintf.o(.text)
 <LI><a href="#[26]">app_key_task</a> from lto-llvm-95641d.o(.text.app_key_task) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[29]">app_motor_key_handle</a> from lto-llvm-95641d.o(.text.app_motor_key_handle) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[28]">app_motor_task</a> from lto-llvm-95641d.o(.text.app_motor_task) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[25]">app_state_task</a> from lto-llvm-95641d.o(.text.app_state_task) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[27]">app_ui_task</a> from lto-llvm-95641d.o(.text.app_ui_task) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[24]">key_scan</a> from lto-llvm-95641d.o(.text.key_scan) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[2e]">my_mem_init</a> from lto-llvm-95641d.o(.text.my_mem_init) referenced from lto-llvm-95641d.o(.data.mallco_dev)
 <LI><a href="#[2f]">my_mem_perused</a> from lto-llvm-95641d.o(.text.my_mem_perused) referenced from lto-llvm-95641d.o(.data.mallco_dev)
 <LI><a href="#[2a]">prvIdleTask</a> from lto-llvm-95641d.o(.text.prvIdleTask) referenced from lto-llvm-95641d.o(.text.main)
 <LI><a href="#[2b]">prvTaskExitError</a> from lto-llvm-95641d.o(.text.prvTaskExitError) referenced from lto-llvm-95641d.o(.text.xTaskCreate)
 <LI><a href="#[23]">vPortSVCHandler_C</a> from lto-llvm-95641d.o(.text.vPortSVCHandler_C) referenced from lto-llvm-95641d.o(.text.SVC_Handler)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[31]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[33]"></a>__scatterload_rt2</STRONG> (Thumb, 74 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[df]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[e0]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[34]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[e1]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[e2]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[78]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[35]"></a>_printf_n</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_n.o(.ARM.Collect$$_printf_percent$$00000001))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_n &rArr; _printf_p &rArr; _printf_f &rArr; _printf_e &rArr; _printf_g &rArr; _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_charcount
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[36]"></a>_printf_p</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_p.o(.ARM.Collect$$_printf_percent$$00000002))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_p &rArr; _printf_f &rArr; _printf_e &rArr; _printf_g &rArr; _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[38]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_e &rArr; _printf_g &rArr; _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[3a]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_g &rArr; _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[3c]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
</UL>

<P><STRONG><a name="[3d]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
</UL>

<P><STRONG><a name="[3e]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_ll &rArr; _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>
<BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>

<P><STRONG><a name="[40]"></a>_printf_i</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_i.o(.ARM.Collect$$_printf_percent$$00000008))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_i &rArr; _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll
</UL>

<P><STRONG><a name="[41]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>

<P><STRONG><a name="[43]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[44]"></a>_printf_o</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_o &rArr; _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
</UL>

<P><STRONG><a name="[45]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[47]"></a>_printf_lli</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lli &rArr; _printf_lld &rArr; _printf_llu &rArr; _printf_llo &rArr; _printf_llx &rArr; _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[49]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_llu &rArr; _printf_llo &rArr; _printf_llx &rArr; _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[4b]"></a>_printf_llu</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_llu &rArr; _printf_llo &rArr; _printf_llx &rArr; _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
</UL>

<P><STRONG><a name="[4c]"></a>_printf_llo</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_llo &rArr; _printf_llx &rArr; _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
</UL>

<P><STRONG><a name="[4d]"></a>_printf_llx</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_llx &rArr; _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_l
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[4f]"></a>_printf_l</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_l.o(.ARM.Collect$$_printf_percent$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_l &rArr; _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[51]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_l
</UL>

<P><STRONG><a name="[52]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[54]"></a>_printf_lc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[56]"></a>_printf_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_ls &rArr; _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent_end
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[58]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[62]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[e3]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[e4]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[5a]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))
<BR><BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[e5]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[e6]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[e7]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[e8]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[e9]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[5c]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[ea]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[eb]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[5d]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000018))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[ec]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[ed]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[ee]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[ef]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[f0]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[f1]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[f2]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[f3]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[f4]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[f5]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[f6]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[f7]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[f8]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[67]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[f9]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[fa]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[fb]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[fc]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[fd]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[fe]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[ff]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[32]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[100]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[5f]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[61]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[101]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[63]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; debug_printf &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[102]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[94]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[66]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[103]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[68]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[6b]"></a>vsprintf</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, vsprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = vsprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
</UL>

<P><STRONG><a name="[6d]"></a>vsnprintf</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, vsnprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[6e]"></a>__2snprintf</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[70]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[71]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[6f]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[42]"></a>_printf_int_dec</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>

<P><STRONG><a name="[76]"></a>__printf</STRONG> (Thumb, 386 bytes, Stack size 32 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[c0]"></a>strcpy</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[c1]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 12 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[da]"></a>strcmp</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, strcmpv6m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_uidivmod</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, aeabi_sdivfast.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG8_IRQHandler
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_idivmod</STRONG> (Thumb, 472 bytes, Stack size 8 bytes, aeabi_sdivfast.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>

<P><STRONG><a name="[104]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[105]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[106]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>_printf_truncate_signed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[73]"></a>_printf_truncate_unsigned</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[75]"></a>_printf_int_common</STRONG> (Thumb, 176 bytes, Stack size 40 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[37]"></a>_printf_charcount</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, _printf_charcount.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[6c]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>

<P><STRONG><a name="[1f]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> vsprintf.o(.text)
</UL>
<P><STRONG><a name="[20]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> vsnprintf.o(.text)
<LI> __2snprintf.o(.text)
</UL>
<P><STRONG><a name="[79]"></a>_printf_cs_common</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_mbtowc (Weak Reference)
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[53]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[55]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[7b]"></a>_printf_wctomb</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, _printf_wctomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>

<P><STRONG><a name="[4a]"></a>_printf_longlong_dec</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[7e]"></a>_printf_longlong_oct</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[46]"></a>_printf_int_oct</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[4e]"></a>_printf_ll_oct</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[7f]"></a>_printf_longlong_hex</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[48]"></a>_printf_int_hex</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[50]"></a>_printf_ll_hex</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[39]"></a>_printf_hex_ptr</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[74]"></a>__rt_udiv10</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, rtudiv10.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[7d]"></a>_ll_udiv10</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[2c]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; btod_internal_mul &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_udiv10
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf1.o(x$fpl$printf1)
</UL>
<P><STRONG><a name="[2d]"></a>_printf_fp_hex_real</STRONG> (Thumb, 718 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf2.o(x$fpl$printf2)
</UL>
<P><STRONG><a name="[88]"></a>_printf_lcs_common</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wc (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[57]"></a>_printf_wchar</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[59]"></a>_printf_wstring</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[8a]"></a>_c16rtomb</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, _c16rtomb.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>

<P><STRONG><a name="[7c]"></a>_wcrtomb</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, _c16rtomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _wcrtomb
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>

<P><STRONG><a name="[107]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[108]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[60]"></a>__user_setup_stackheap</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[8b]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_c16rtomb
</UL>

<P><STRONG><a name="[5b]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[86]"></a>_printf_fp_infnan</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[81]"></a>_btod_etento</STRONG> (Thumb, 210 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; btod_internal_mul &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[82]"></a>_btod_d2e</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, btod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _btod_d2e
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[84]"></a>_btod_emul</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, btod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _btod_emul &rArr; btod_internal_mul &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btod_internal_mul
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[90]"></a>_btod_emuld</STRONG> (Thumb, 144 bytes, Stack size 56 bytes, btod.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btod_internal_mul
</UL>

<P><STRONG><a name="[83]"></a>_btod_ediv</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, btod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _btod_ediv &rArr; btod_internal_div
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btod_internal_div
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[92]"></a>_btod_edivd</STRONG> (Thumb, 124 bytes, Stack size 56 bytes, btod.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btod_internal_div
</UL>

<P><STRONG><a name="[65]"></a>exit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_call_atexit_fns (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[96]"></a>__fpl_cmpreturn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, cmpret.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[95]"></a>__fpl_return_NaN</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, retnan.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
</UL>

<P><STRONG><a name="[69]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[109]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[10a]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>__aeabi_memset</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, aeabi_memset.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memset
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[10b]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>__aeabi_memset4</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, aeabi_memset4.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memset4
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[10c]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, aeabi_memset4.o(.text), UNUSED)

<P><STRONG><a name="[9a]"></a>_memset_w</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr_w
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset4
</UL>

<P><STRONG><a name="[98]"></a>_memset</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[9b]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[10d]"></a>__rt_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[ad]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>__rt_memclr_w</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[bd]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[10f]"></a>__extendsfdf2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, f2d.o(.text), UNUSED)

<P><STRONG><a name="[110]"></a>_f2d</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, f2d.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 20 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[111]"></a>__divsf3</STRONG> (Thumb, 0 bytes, Stack size 20 bytes, fdiv.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>_fdiv</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frdiv
</UL>

<P><STRONG><a name="[9d]"></a>_frdiv</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
</UL>

<P><STRONG><a name="[b5]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ffixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[112]"></a>_ffix</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ffixi.o(.text), UNUSED)

<P><STRONG><a name="[a0]"></a>__aeabi_i2f_normalise</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_i2f</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[113]"></a>_fflt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__aeabi_ui2f</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>

<P><STRONG><a name="[114]"></a>_ffltu</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[a2]"></a>__fpl_fcmp_InfNaN</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, fcmpin.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[a3]"></a>__fpl_fcheck_NaN2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, fnan2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[ce]"></a>DL_ADC12_setClockConfig</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_ADC12_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_ADC12_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>DL_Common_delayCycles</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cf]"></a>DL_DMA_initChannel</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dl_dma.o(.text.DL_DMA_initChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_DMA_initChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>DL_SYSCTL_configSYSPLL</STRONG> (Thumb, 196 bytes, Stack size 36 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DL_SYSCTL_configSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c4]"></a>DL_SYSCTL_setHFCLKSourceHFXTParams</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c6]"></a>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c8]"></a>DL_Timer_initFourCCPWMMode</STRONG> (Thumb, 264 bytes, Stack size 20 bytes, dl_timer.o(.text.DL_Timer_initFourCCPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_Timer_initFourCCPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cb]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 240 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_initTimerMode
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c9]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[c7]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cd]"></a>DL_UART_init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[cc]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, lto-llvm-95641d.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GROUP1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.HardFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = HardFault_Handler &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.PendSV_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, lto-llvm-95641d.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysTick_Handler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.TIMG8_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMG8_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, lto-llvm-95641d.o(.text.UART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.UART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>main</STRONG> (Thumb, 2524 bytes, Stack size 72 bytes, lto-llvm-95641d.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = main &rArr; debug_printf &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initFourCCPWMMode
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_data
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vStartFirstTask
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[23]"></a>vPortSVCHandler_C</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lto-llvm-95641d.o(.text.vPortSVCHandler_C))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vPortSVCHandler_C
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vRestoreContextOfFirstTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.SVC_Handler)
</UL>
<P><STRONG><a name="[30]"></a>vTaskSwitchContext</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, lto-llvm-95641d.o(.text.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[c2]"></a>__aeabi_uidiv</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, aeabi_sdivfast.o(.text_divfast))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_perused
</UL>

<P><STRONG><a name="[115]"></a>__aeabi_idiv</STRONG> (Thumb, 434 bytes, Stack size 8 bytes, aeabi_sdivfast.o(.text_divfast), UNUSED)

<P><STRONG><a name="[8f]"></a>__ARM_common_ll_muluu</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, btod.o(i.__ARM_common_ll_muluu))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __ARM_common_ll_muluu
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btod_internal_mul
</UL>

<P><STRONG><a name="[85]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[ba]"></a>__aeabi_fcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgeq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[d5]"></a>_fgeq</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgeq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[b6]"></a>__aeabi_fcmpgt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgr))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[d7]"></a>_fgr</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgr), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_fcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fleq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[d8]"></a>_fleq</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, fcmp.o(i._fleq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[77]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[5e]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _get_lc_numeric &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[22]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _get_lc_ctype &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[b7]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[db]"></a>_fadd</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[d6]"></a>_fcmpge</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fgef.o(x$fpl$fgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgr
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgeq
</UL>

<P><STRONG><a name="[116]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)

<P><STRONG><a name="[d9]"></a>_fcmple</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fleq
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[117]"></a>_fmul</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)

<P><STRONG><a name="[b8]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[dd]"></a>_fsub</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>

<P><STRONG><a name="[3b]"></a>_printf_fp_dec</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _printf_fp_dec
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[3f]"></a>_printf_fp_hex</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _printf_fp_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[21]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[80]"></a>_fp_digits</STRONG> (Thumb, 412 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; btod_internal_mul &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[8e]"></a>btod_internal_mul</STRONG> (Thumb, 492 bytes, Stack size 56 bytes, btod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = btod_internal_mul &rArr; __ARM_common_ll_muluu
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_ll_muluu
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emuld
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[91]"></a>btod_internal_div</STRONG> (Thumb, 520 bytes, Stack size 64 bytes, btod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = btod_internal_div
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_edivd
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[b1]"></a>system_soft_reset</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.system_soft_reset))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[a4]"></a>my_printf</STRONG> (Thumb, 344 bytes, Stack size 40 bytes, lto-llvm-95641d.o(.text.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[bc]"></a>debug_printf</STRONG> (Thumb, 440 bytes, Stack size 64 bytes, lto-llvm-95641d.o(.text.debug_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = debug_printf &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[b0]"></a>debug_message</STRONG> (Thumb, 444 bytes, Stack size 56 bytes, lto-llvm-95641d.o(.text.debug_message))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = debug_message &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[24]"></a>key_scan</STRONG> (Thumb, 280 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.key_scan))
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[d3]"></a>i2c_wait_ack</STRONG> (Thumb, 476 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2c_wait_ack
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_data
</UL>

<P><STRONG><a name="[d2]"></a>i2c_send_byte</STRONG> (Thumb, 1196 bytes, Stack size 28 bytes, lto-llvm-95641d.o(.text.i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = i2c_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_data
</UL>

<P><STRONG><a name="[a9]"></a>soft_i2c_write_data</STRONG> (Thumb, 476 bytes, Stack size 40 bytes, lto-llvm-95641d.o(.text.soft_i2c_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = soft_i2c_write_data &rArr; i2c_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a8]"></a>OLED_Update</STRONG> (Thumb, 460 bytes, Stack size 56 bytes, lto-llvm-95641d.o(.text.OLED_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = OLED_Update &rArr; soft_i2c_write_data &rArr; i2c_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;soft_i2c_write_data
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[a7]"></a>OLED_ShowImage</STRONG> (Thumb, 644 bytes, Stack size 44 bytes, lto-llvm-95641d.o(.text.OLED_ShowImage))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[a6]"></a>OLED_ShowString</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
</UL>

<P><STRONG><a name="[a5]"></a>OLED_Printf</STRONG> (Thumb, 32 bytes, Stack size 280 bytes, lto-llvm-95641d.o(.text.OLED_Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = OLED_Printf &rArr; vsprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[d0]"></a>xTaskCreate</STRONG> (Thumb, 912 bytes, Stack size 40 bytes, lto-llvm-95641d.o(.text.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset4
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d1]"></a>vStartFirstTask</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.vStartFirstTask))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[25]"></a>app_state_task</STRONG> (Thumb, 1480 bytes, Stack size 72 bytes, lto-llvm-95641d.o(.text.app_state_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = app_state_task &rArr; debug_printf &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_perused
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[26]"></a>app_key_task</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.app_key_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = app_key_task &rArr; debug_message &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_soft_reset
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[27]"></a>app_ui_task</STRONG> (Thumb, 360 bytes, Stack size 64 bytes, lto-llvm-95641d.o(.text.app_ui_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = app_ui_task &rArr; OLED_Printf &rArr; vsprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[28]"></a>app_motor_task</STRONG> (Thumb, 1800 bytes, Stack size 160 bytes, lto-llvm-95641d.o(.text.app_motor_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = app_motor_task &rArr; debug_printf &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[29]"></a>app_motor_key_handle</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.app_motor_key_handle))
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[2a]"></a>prvIdleTask</STRONG> (Thumb, 400 bytes, Stack size 8 bytes, lto-llvm-95641d.o(.text.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvIdleTask &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.main)
</UL>
<P><STRONG><a name="[af]"></a>xTaskDelayUntil</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, lto-llvm-95641d.o(.text.xTaskDelayUntil))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = xTaskDelayUntil &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[2f]"></a>my_mem_perused</STRONG> (Thumb, 200 bytes, Stack size 8 bytes, lto-llvm-95641d.o(.text.my_mem_perused))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = my_mem_perused
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.data.mallco_dev)
</UL>
<P><STRONG><a name="[be]"></a>pvPortMalloc</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[bf]"></a>xTaskResumeAll</STRONG> (Thumb, 300 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[ae]"></a>vTaskDelete</STRONG> (Thumb, 516 bytes, Stack size 24 bytes, lto-llvm-95641d.o(.text.vTaskDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = vTaskDelete &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[b4]"></a>pid_calculate_incremental</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, lto-llvm-95641d.o(.text.pid_calculate_incremental))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = pid_calculate_incremental &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_motor_task
</UL>

<P><STRONG><a name="[d4]"></a>vRestoreContextOfFirstTask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.vRestoreContextOfFirstTask))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSVCHandler_C
</UL>

<P><STRONG><a name="[aa]"></a>ulSetInterruptMask</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lto-llvm-95641d.o(.text.ulSetInterruptMask))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[ab]"></a>xTaskIncrementTick</STRONG> (Thumb, 312 bytes, Stack size 32 bytes, lto-llvm-95641d.o(.text.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[2b]"></a>prvTaskExitError</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, lto-llvm-95641d.o(.text.prvTaskExitError))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = prvTaskExitError
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.text.xTaskCreate)
</UL>
<P><STRONG><a name="[2e]"></a>my_mem_init</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, lto-llvm-95641d.o(.text.my_mem_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = my_mem_init
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lto-llvm-95641d.o(.data.mallco_dev)
</UL>
<P><STRONG><a name="[de]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[dc]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[93]"></a>_call_atexit_fns</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[7a]"></a>_printf_mbtowc</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[89]"></a>_printf_wc</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<HR></body></html>
