#ifndef __MOTOR_H_
#define __MOTOR_H_

/**
 * @brief  初始化电机
 * @param  None
 * @retval None
 */
void motor_init(void);

/**
 * @brief  电机初始化
 * @param  motor_id: 电机编号
 * @param  direction: 方向 0:停止 1:正转 2:反转
 * @retval None
 */
void motor_set_direction(uint8_t motor_id, uint8_t direction);

/**
 * @brief  设置电机PWM值
 * @param  motor_id: 电机编号
 * @param  pwm_value: PWM值
 * @retval None
 */
void motor_set_pwm_value(uint8_t motor_id, uint16_t pwm_value);

/**
 * @brief  设置电机速度
 * @param  motor_id: 电机编号
 * @param  speed: 速度
 * @retval None
 */
void motor_set_speed(uint8_t motor_id, int16_t speed);

/**
 * @brief  获取电机速度
 * @param  encoder_id: 编码器编号
 * @param  time_ms: 时间
 * @retval 速度 单位: cm/s
 */
float motor_get_speed(uint8_t encoder_id, uint16_t time_ms);

/**
 * @brief  获取电机距离
 * @param  encoder_id: 编码器编号
 * @retval 距离 单位: cm
 */
int32_t motor_get_distance(uint8_t encoder_id);

/**
 * @brief  设置电机距离
 * @param  encoder_id: 编码器编号
 * @retval None
 */
void motor_reset_distance(uint8_t encoder_id, int32_t distance);

#endif
