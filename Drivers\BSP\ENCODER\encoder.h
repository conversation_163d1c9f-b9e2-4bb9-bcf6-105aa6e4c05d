#ifndef __ENCODER_H_
#define __ENCODER_H_

/**
 * @brief     中断模式的编码器初始化
 * @param     无
 * @retval    无
 */
void encoder_int_init(void);

/**
 * @brief     编码器中断回调函数
 * @param     无
 * @retval    无
 */
void encoder_int_callback(uint32_t iidx);

/**
 * @brief     获取编码器中断计数值
 * @param     encoder_int_id 编码器中断ID
 * @retval    编码器中断计数值
 */
short encoder_int_get_value(uint8_t encoder_int_id);

/**
 * @brief     获取编码器累计总计数值
 * @param     encoder_id 编码器ID
 * @retval    累计总计数值
 */
int32_t encoder_get_total_count(uint8_t encoder_id);

/**
 * @brief     设置编码器累计总计数值
 * @param     encoder_id 编码器ID
 * @param     value 累计总计数值
 * @retval    无
 */
void encoder_set_total_count(uint8_t encoder_id, int32_t value);

/**
 * @brief     清除编码器中断计数值
 * @param     encoder_int_id 编码器中断ID
 * @param     value 编码器中断计数值
 * @retval    无
 */
void encoder_int_set_value(uint8_t encoder_int_id, short value);

#endif