Dependencies for Project 'project', Target 'Project': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (..\..\Drivers\source\ti\devices\msp\m0p\startup_system_files\keil\startup_mspm0g350x_uvision.s)(0x688B6AF7)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ../../output/output/startup_mspm0g350x_uvision.o)
F (.\ti_msp_dl_config.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/ti_msp_dl_config.o -MMD)
I (ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
F (.\ti_msp_dl_config.h)(0x688B6AFC)()
F (.\project.syscfg)(0x688B6AFC)()
F (.\syscfg.bat)(0x688B6AFC)()
F (..\..\System\board_config.h)(0x688B6AFC)()
F (..\..\Drivers\BSP\LED\led.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/led.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\LED\led.h)(0x688B6AF3)
F (..\..\Drivers\BSP\SYSTEM\system.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/system.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
F (..\..\Drivers\BSP\DELAY\delay.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/delay.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DELAY\delay.h)(0x688B6AF2)
F (..\..\Drivers\BSP\DEBUG\debug.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/debug.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
F (..\..\Drivers\BSP\KEY\gpio_key.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/gpio_key.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\KEY\gpio_key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
F (..\..\Drivers\BSP\KEY\key.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/key.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\KEY\gpio_key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\KEY\adc_key.h)(0x688B6AF3)
F (..\..\Drivers\BSP\ADC\bsp_adc.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/bsp_adc.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\ADC\bsp_adc.h)(0x688B6AF2)
F (..\..\Drivers\BSP\KEY\adc_key.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/adc_key.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\KEY\adc_key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ADC\bsp_adc.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
F (..\..\Drivers\BSP\SOFT_I2C\soft_i2c.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/soft_i2c.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DELAY\delay.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\SOFT_I2C\soft_i2c.h)(0x688B6AF3)
F (..\..\Drivers\BSP\OLED\OLED.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/oled.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\SOFT_I2C\soft_i2c.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED_Data.h)(0x688B6AF3)
F (..\..\Drivers\BSP\OLED\OLED_Data.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/oled_data.o -MMD)
I (..\..\Drivers\BSP\OLED\OLED_Data.h)(0x688B6AF3)
F (..\..\Drivers\BSP\TB6612\tb6612.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/tb6612.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\TB6612\tb6612.h)(0x688B6AF3)
F (..\..\Drivers\BSP\MOTOR\motor.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/motor.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\MOTOR\motor.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\TB6612\tb6612.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
F (..\..\Drivers\BSP\JYxx\jy60.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/jy60.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\JYxx\jy60.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\DELAY\delay.h)(0x688B6AF2)
F (..\..\Drivers\BSP\JYxx\jyxx.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/jyxx.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\JYxx\jy60.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\JYxx\jyxx.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED_Data.h)(0x688B6AF3)
F (..\..\Drivers\BSP\GW_TRACK\gw_track.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/gw_track.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\SOFT_I2C\soft_i2c.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\GW_TRACK\gw_track.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\OLED\OLED.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED_Data.h)(0x688B6AF3)
F (..\..\Drivers\BSP\ENCODER\encoder.c)(0x688B6AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/encoder.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
F (..\..\App\main.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/main.o -MMD)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\LED\led.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\DELAY\delay.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ADC\bsp_adc.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\OLED\OLED.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED_Data.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\MOTOR\motor.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\JYxx\jyxx.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\GW_TRACK\gw_track.h)(0x688B6AF3)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\App\app_state.h)(0x688B6AF2)
I (..\..\App\app_key.h)(0x688B6AF2)
I (..\..\App\app_ui.h)(0x688B6AF2)
I (..\..\App\app_motor.h)(0x688B6AF2)
I (..\..\Middlewares\.\MALLOC\malloc.h)(0x688B6AFC)
F (..\..\App\app_state.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/app_state.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\LED\led.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Middlewares\.\MALLOC\malloc.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\App\app_state.h)(0x688B6AF2)
F (..\..\App\app_key.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/app_key.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\App\app_key.h)(0x688B6AF2)
F (..\..\App\app_ui.c)(0x688B6AF2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/app_ui.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\OLED\OLED.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\OLED\OLED_Data.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\App\app_ui.h)(0x688B6AF2)
F (..\..\App\app_motor.c)(0x688B7194)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/app_motor.o -MMD)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\DEBUG\debug.h)(0x688B6AF2)
I (..\..\Drivers\.\BSP\KEY\key.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\LED\led.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\GW_TRACK\gw_track.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\JYxx\jyxx.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\MOTOR\motor.h)(0x688B6AF3)
I (..\..\Drivers\.\BSP\ENCODER\encoder.h)(0x688B6AF3)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\.\PID\pid.h)(0x688B6AFC)
I (..\..\App\app_motor.h)(0x688B6AF2)
I (..\..\App\app_key.h)(0x688B6AF2)
F (..\..\Middlewares\freertos\src\croutine.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/croutine.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\croutine.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\event_groups.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/event_groups.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\timers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\event_groups.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\list.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/list.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\mpu_wrappers_v2_asm.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/mpu_wrappers_v2_asm.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\queue.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\timers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\event_groups.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\stream_buffer.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_prototypes.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_syscall_numbers.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\port.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/port.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_syscall_numbers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portasm.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\portasm.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/portasm.o -MMD)
I (..\..\Middlewares\freertos\include\portasm.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_syscall_numbers.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\queue.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/queue.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\queue.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\stream_buffer.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/stream_buffer.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\stream_buffer.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\tasks.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/tasks.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\timers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\stack_macros.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\timers.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/timers.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\queue.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\timers.h)(0x688B6AFC)
F (..\..\Middlewares\freertos\src\heap_4.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/heap_4.o -MMD)
I (..\..\Middlewares\freertos\include\FreeRTOS.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\FreeRTOSConfig.h)(0x688B6AFC)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Middlewares\freertos\include\projdefs.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portable.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\deprecated_definitions.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\portmacro.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\mpu_wrappers.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\task.h)(0x688B6AFC)
I (..\..\Middlewares\freertos\include\list.h)(0x688B6AFC)
F (..\..\Middlewares\lwrb\lwrb.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/lwrb.o -MMD)
I (..\..\Middlewares\lwrb\lwrb.h)(0x688B6AFC)
F (..\..\Middlewares\lwrb\lwrb_ex.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/lwrb_ex.o -MMD)
I (..\..\Middlewares\lwrb\lwrb.h)(0x688B6AFC)
F (..\..\Middlewares\MALLOC\malloc.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/malloc.o -MMD)
I (..\..\Middlewares\.\MALLOC\malloc.h)(0x688B6AFC)
I (..\..\Drivers\.\BSP\SYSTEM\system.h)(0x688B6AF3)
I (..\MDK\ti_msp_dl_config.h)(0x688B6AFC)
I (..\..\Drivers\source\ti\devices\msp\msp.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\DeviceFamily.h)(0x688B6AF6)
I (..\..\Drivers\source\ti\devices\msp\m0p\mspm0g350x.h)(0x688B6AF6)
I (..\..\Drivers\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x688B6AF3)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_iomux.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_oa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_spi.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_trng.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_uart.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_vref.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wuc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\driverlib.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_adc12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_factoryregion.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_core.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_aes.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_aesadv.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_comp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_crcp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dac12.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_dma.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_flashctl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_sysctl.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\dl_gpamp.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_gpio.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_i2c.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_iwdt.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lfss.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_keystorectl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_lcd.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mathacl.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_mcan.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_opa.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_common.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_a.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_rtc_b.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_scratchpad.h)(0x688B6AF7)
I (..\..\Drivers\source\ti\driverlib\dl_spi.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_tamperio.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timera.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timer.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_timerg.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_trng.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_extend.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_uart_main.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_vref.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\dl_wwdt.h)(0x688B6AF8)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_interrupt.h)(0x688B6AFB)
I (..\..\Drivers\source\ti\driverlib\m0p\dl_systick.h)(0x688B6AFB)
I (..\..\Drivers\source\..\..\System\board_config.h)(0x688B6AFC)
F (..\..\Middlewares\pid\pid.c)(0x688B6AFC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -flto -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../Drivers/source/third_party/CMSIS/Core/Include -I ../../Drivers/source -I ../../Drivers -I ../MDK -I ../../Middlewares/freertos/include -I ../../Middlewares/freertos/portable/RVDS/ARM_CM0 -I ../../App -I ../../Middlewares -Wno-invalid-source-encoding

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ../../output/output/pid.o -MMD)
I (..\..\Middlewares\.\PID\PID.h)(0x688B6AFC)
