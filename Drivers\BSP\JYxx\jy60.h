#ifndef __JY60_H_
#define __JY60_H_

// clang-format off
#define CMD_ZERO_ANGLE_Z                (0x52) // Z轴角度归零
#define CMD_ACCELERATION_CALIBRATION    (0x67) // 加计校准
#define CMD_WAKE_OR_SLEEP               (0x60) // 休眠与解休眠
#define CMD_UART_MODE                   (0x61) // 串口模式
#define CMD_IIC_MODE                    (0x62) // IIC模式
#define CMD_UART_115200                 (0x63) // 串口波特率115200
#define CMD_UART_9600                   (0x64) // 串口波特率9600
#define CMD_HORIZONTAL                  (0x65) //水平模式
#define CMD_VERTICAL                    (0x66) //垂直模式
#define CMD_BANDWIDTH_256HZ             (0x81) //带宽256HZ  
#define CMD_BANDWIDTH_188HZ             (0x82) //带宽188HZ  
#define CMD_BANDWIDTH_98HZ              (0x83) //带宽98HZ
#define CMD_BANDWIDTH_42HZ              (0x84) //带宽42HZ
#define CMD_BANDWIDTH_20HZ              (0x85) //带宽20HZ
#define CMD_BANDWIDTH_10HZ              (0x86) //带宽10HZ
#define CMD_BANDWIDTH_5HZ               (0x87) //带宽5HZ


#define TYPE_HEAD                       (0x55) // 数据头
#define TYPE_ACCELERATED_SPEED          (0x51) // 加速度
#define TYPE_ANGLE_SPEED                (0x52) // 角速度
#define TYPE_ANGLE                      (0x53) // 角度
// clang-format on

/**
 * @brief       向JY60发送指令
 * @param       cmd: 指令
 * @retval      无
 */
void jy60_send_command(uint8_t cmd);

/**
 * @brief       设置JY60休眠或唤醒
 * @param       state: 休眠或唤醒   1:休眠   0:唤醒
 * @retval      无
 */
void jy60_set_sleep_wake(uint8_t state);

/**
 * @brief       JY60串口接收回调函数
 * @param       data: 接收到的数据
 * @retval      无
 */
static void jy60_uart_recv_callback(uint8_t data);

/**
 * @brief       更新JY60数据
 * @param       无
 * @retval      无
 */
void jy60_up_data(void);

/**
 * @brief       获取JY60加速度
 * @param       加速度
 * @retval
 */
float *jy60_get_acceleration(void);

/**
 * @brief       获取JY60角速度
 * @param       无
 * @retval      角速度
 */
float *jy60_get_angle_speed(void);

/**
 * @brief       获取JY60角度
 * @param       无
 * @retval      角度
 */
float *jy60_get_angle(void);

/**
 * @brief       获取JY60温度
 * @param       无
 * @retval      温度
 */
float jy60_get_temperature(void);

/**
 * @brief       获取JY60电压
 * @param       无
 * @retval      电压
 */
float jy60_get_voltage(void);

/**
 * @brief       获取JY60版本
 * @param       无
 * @retval      版本
 */
float jy60_get_version(void);

/**
 * @brief       JY60初始化
 * @param       无
 * @retval      无
 */
void jy60_init(void);

#endif
