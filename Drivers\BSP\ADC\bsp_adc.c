#include "./BSP/SYSTEM/system.h"
#include "./BSP/ADC/bsp_adc.h"

static uint8_t adc_pin_value_map[CFG_ADC_NUM] = {CFG_ADC_READ_BUF};
static uint16_t adc_value[CFG_ADC_NUM];

/**
 * @brief       BSP_ADC初始化
 * @param       无
 * @retval      无
 */
void bsp_adc_init(void)
{
    // 设置DMA搬运的起始地址
    DL_DMA_setSrcAddr(DMA, CFG_ADC_DMA_CH, (uint32_t)&ADC_INST->ULLMEM.MEMRES[0]);
    // 设置DMA搬运的目的地址
    DL_DMA_setDestAddr(DMA, CFG_ADC_DMA_CH, (uint32_t)&adc_value[0]);
    // 开启DMA
    DL_DMA_enableChannel(DMA, CFG_ADC_DMA_CH);
    // 开启ADC转换
    DL_ADC12_startConversion(ADC_INST);
}

/**
 * @brief       获取ADC值
 * @param       index:ADC引脚索引
 * @retval      adc值
 */
uint16_t bsp_adc_get_value(uint8_t index)
{
    for (uint8_t i = 0; i < CFG_ADC_NUM; i++)
    {
        if (adc_pin_value_map[i] == index)
        {
            return adc_value[i];
        }
    }
	return 0xFF;
}