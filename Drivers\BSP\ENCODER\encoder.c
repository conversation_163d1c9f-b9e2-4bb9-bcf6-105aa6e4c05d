#include "./BSP/SYSTEM/system.h"

#include "./BSP/ENCODER/encoder.h"

const uint8_t encoder_a_irqn[] = {CFG_ENCODER_A_INT_IRQN};
const uint32_t encoder_a_iidx[] = {CFG_ENCODER_A_INT_IIDX};

const GPIO_Regs *encoder_a_port[] = {CFG_ENCODER_A_PORT};
const uint32_t encoder_a_pin[] = {CFG_ENCODER_A_PIN};

const GPIO_Regs *encoder_b_port[] = {CFG_ENCODER_B_PORT};
const uint32_t encoder_b_pin[] = {CFG_ENCODER_B_PIN};

static short encoder_int_cnt[CFG_ENCODER_INT_NUM] = {0};
static int32_t encoder_total_count[CFG_ENCODER_INT_NUM] = {0};

/**
 * @brief     中断模式的编码器初始化
 * @param     无
 * @retval    无
 */
void encoder_int_init(void)
{
    for (uint8_t i = 0; i < CFG_ENCODER_INT_NUM; i++)
    {
        NVIC_EnableIRQ(encoder_a_irqn[i]);
    }
}

/**
 * @brief     编码器中断回调函数
 * @param     无
 * @retval    无
 */
void encoder_int_callback(uint32_t iidx)
{

    for (uint8_t i = 0; i < CFG_ENCODER_INT_NUM; i++)
    {
        // if (iidx == encoder_a_iidx[i])
        // {
        //     uint32_t pin_state = DL_GPIO_readPins((GPIO_Regs *)encoder_b_port[i], encoder_b_pin[i]);

        //     if (pin_state & encoder_b_pin[i])
        //     {
        //         encoder_int_cnt[i]++;
        //     }
        //     else
        //     {
        //         encoder_int_cnt[i]--;
        //     }
        // }

        uint32_t gpio_status = DL_GPIO_getEnabledInterruptStatus((GPIO_Regs *)encoder_a_port[i], encoder_a_pin[i]);
        // 编码器A相上升沿触发
        if ((gpio_status & encoder_a_pin[i]) == encoder_a_pin[i])
        {
            uint32_t pin_state = DL_GPIO_readPins((GPIO_Regs *)encoder_b_port[i], encoder_b_pin[i]);

            if (pin_state & encoder_b_pin[i])
            {
                encoder_int_cnt[i]++;
            }
            else
            {
                encoder_int_cnt[i]--;
            }
        } // 编码器B相上升沿触发
        DL_GPIO_clearInterruptStatus((GPIO_Regs *)encoder_a_port[i], encoder_a_pin[i]);
    }
}

/**
 * @brief     获取编码器中断计数值
 * @param     encoder_int_id 编码器中断ID
 * @retval    编码器中断计数值
 */
short encoder_int_get_value(uint8_t encoder_int_id)
{
    return encoder_int_cnt[encoder_int_id];
}

/**
 * @brief     清除编码器中断计数值
 * @param     encoder_int_id 编码器中断ID
 * @param     value 编码器中断计数值
 * @retval    无
 */
void encoder_int_set_value(uint8_t encoder_int_id, short value)
{
    encoder_int_cnt[encoder_int_id] = 0;
}

/**
 * @brief     获取编码器累计总计数值
 * @param     encoder_id 编码器ID
 * @retval    累计总计数值
 */
int32_t encoder_get_total_count(uint8_t encoder_id)
{
    return encoder_total_count[encoder_id];
}

/**
 * @brief     设置编码器累计总计数值
 * @param     encoder_id 编码器ID
 * @param     value 累计总计数值
 * @retval    无
 */
void encoder_set_total_count(uint8_t encoder_id, int32_t value)
{
    encoder_total_count[encoder_id] = value;
}