#include "./BSP/SYSTEM/system.h"

#include "./BSP/DEBUG/debug.h"
#include "./BSP/OLED/OLED.h"
#include "./BSP/ENCODER/encoder.h"

#include "FreeRTOS.h"
#include "task.h"

#include "app_ui.h"

uint8_t is_app_ui_task_start = 0;

/**
 * @brief       UI显示任务
 * @param       arg: 传入的参数
 * @retval      无
 */
static void app_ui_task(void *arg)
{
    uint16_t count = 0;
    OLED_ShowString(0, 0, "hello world", OLED_6X8);
    OLED_Update();
    TickType_t tick;
    tick = xTaskGetTickCount();
    while (is_app_ui_task_start)
    {
        OLED_Printf(0, 8, OLED_6X8, "count: %d", count++);
        // short val = encoder_int_get_value(0);
        // OLED_Printf(0, 16, OLED_6X8, "val0: %5d  ", val);
        // val = encoder_int_get_value(1);
        // OLED_Printf(0, 24, OLED_6X8, "val1: %5d  ", val);

        extern void jyxx_oled_show(void);
        jyxx_oled_show();

        extern void track_oled_show(void);
        track_oled_show();

        OLED_Update();

        vTaskDelayUntil(&tick, 100);
    }
    vTaskDelete(NULL);
}

/**
 * @brief       启动UI显示任务
 * @param       无
 * @retval      1:失败 0:成功
 */
uint8_t app_ui_task_start(void)
{
    if (is_app_ui_task_start)
        return 1;

    if (xTaskCreate(app_ui_task, "app_ui_task", 256, NULL, 0, NULL) != pdPASS)
    {
        return 1;
    }
    is_app_ui_task_start = 1;
    return 0;
}

/**
 * @brief       停止UI显示任务
 * @param       无
 * @retval      1:失败 0:成功
 */
void app_ui_task_stop(void)
{
    is_app_ui_task_start = 0;
}
