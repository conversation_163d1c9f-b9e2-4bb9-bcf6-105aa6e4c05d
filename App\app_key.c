#include "./BSP/SYSTEM/system.h"

#include "./BSP/DEBUG/debug.h"
#include "./BSP/KEY/key.h"

#include "FreeRTOS.h"
#include "task.h"

#include "app_key.h"

static uint8_t is_app_key_task_create;

static void app_key_common(uint8_t event, uint8_t value)
{
    if (event == KEY_EVENT_LONG_PRESS && value == 5)
    {
        system_soft_reset(); // 长按 按键6 复位系统
    }
    
}

/**
 * @brief       按键处理任务
 * @param       arg: 传入的参数
 * @retval      无
 */
static void app_key_task(void *arg)
{
    key_state_t key_state;
    TickType_t tick;
    tick = xTaskGetTickCount();
    while (is_app_key_task_create)
    {
        vTaskDelayUntil(&tick, 20);

        key_get_value(&key_state);

        if (key_state.value == NO_KEY)
            continue;

        DEBUG_INFO("event %d ,value %d\r\n", key_state.event, key_state.value);

        app_key_common(key_state.event, key_state.value); // 按键通用处理

        key_callback_process(key_state.event, key_state.value); // 按键回调处理
    }
    vTaskDelete(NULL);
}

/**
 * @brief       启动按键处理任务
 * @param       无
 * @retval      1:失败 0:成功
 */
uint8_t app_key_task_start(void)
{
    if (is_app_key_task_create != 0)
    {
        return 1;
    }

    if (xTaskCreate(app_key_task, "app_key_task_start", 256, NULL, 1, NULL) != pdPASS)
    {
        return 1;
    }
    is_app_key_task_create = 1;
    return 0;
}

/**
 * @brief       停止按键处理任务
 * @param       无
 * @retval      无
 */
void app_key_task_stop(void)
{
    is_app_key_task_create = 0;
}
