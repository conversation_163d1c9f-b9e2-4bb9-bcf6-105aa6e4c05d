#include "./BSP/SYSTEM/system.h"

#include "./BSP/DEBUG/debug.h"
#include "./BSP/KEY/key.h"
#include "./BSP/LED/led.h"
#include "./BSP/GW_TRACK/gw_track.h"
#include "./BSP/JYxx/jyxx.h"
#include "./BSP/MOTOR/motor.h"
#include "./BSP/ENCODER/encoder.h"
#include "./BSP/LED/led.h"

#include "FreeRTOS.h"
#include "task.h"

#include "./PID/pid.h"

#include "app_motor.h"
#include "app_key.h"

PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;  // 循迹环
PID_T pid_angle; // 角度环

float basic_speed = 0; // 基础速度

// 低通滤波器系数 (Low-pass filter coefficient 'alpha')
// alpha 越小, 滤波效果越强, 但延迟越大。建议从 0.1 到 0.5 之间开始尝试。
#define SPEED_FILTER_ALPHA_LEFT 0.15f
#define SPEED_FILTER_ALPHA_RIGHT 0.15f

#define MOTOR_L 0
#define MOTOR_R 1

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;

PidParams_t pid_params_speed_left = {
    .kp = 10.0f,
    .ki = 0.35f,
    .kd = 0.4f,
    .out_min = -600,
    .out_max = 600,
};

PidParams_t pid_params_speed_right = {
    .kp = 10.0f,
    .ki = 0.35f,
    .kd = 0.4f,
    .out_min = -600,
    .out_max = 600,
};

// 巡线控制PID (位置式)
PidParams_t pid_params_line = {
    .kp = 3.5f,
    .ki = 0.0f,
    .kd = 0.4f,
    .out_min = -600,
    .out_max = 600,
};

// 角度环（偏航角）
PidParams_t pid_params_angle = {
    .kp = 0.35f, // 增量式PID的P参数
    .ki = 0.3f,  // 增量式PID的I参数  不适宜给太大 因为他会积累误差
    .kd = 0.3f,  // 增量式PID的D参数，不宜过大
    .out_min = -1000,
    .out_max = 1000,
};

void pid_test(uint16_t p, uint16_t i, uint16_t d)
{
}

void motor_pid_init(void)
{
    // 初始化左轮速度PID控制器
    pid_init(&pid_speed_left,
             pid_params_speed_left.kp, pid_params_speed_left.ki, pid_params_speed_left.kd,
             0.0f, pid_params_speed_left.out_max);

    // 初始化右轮速度PID控制器
    pid_init(&pid_speed_right,
             pid_params_speed_right.kp, pid_params_speed_right.ki, pid_params_speed_right.kd,
             0.0f, pid_params_speed_right.out_max);

    // // 初始化直线行驶PID控制器
    pid_init(&pid_line,
             pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
             0.0f, pid_params_line.out_max);

    // pid_init(&pid_angle,
    //          pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
    //          0.0f, pid_params_angle.out_max);

    // 设置左轮速度PID控制器的目标值
    pid_set_target(&pid_speed_left, basic_speed);
    // 设置右轮速度PID控制器的目标值
    pid_set_target(&pid_speed_right, basic_speed);

    pid_set_target(&pid_line, 0);

    // pid_set_target(&pid_angle, 0);
}

static uint8_t is_app_motor_task_create = 0;
static uint8_t motor_mode = 0;
uint8_t motor_led_state = 0;

/**
 * @brief       电机按键处理函数
 * @param       key_event: 按键事件
 * @param       key_value: 按键值
 * @retval      无
 */
static void app_motor_key_handle(uint8_t key_event, uint8_t key_value)
{
    if (key_event == KEY_EVENT_LONG_PRESS)
    {
        if (key_value == 1)
        {
            motor_mode = 0;
        }
        else if (key_value == 2)
        {
        }
        else if (key_value == 3)
        {
        }
        else if (key_value == 4)
        {
        }
    }

    if (key_event == KEY_EVENT_CLICK)
    {
        if (key_value == 1)
        {
            motor_mode = 1;
        }
        else if (key_value == 2)
        {
            motor_mode = 2;
        }
        else if (key_value == 3)
        {
            motor_mode = 3;
        }
        else if (key_value == 4)
        {
            motor_mode = 4;
        }
        else if (key_value == 5)
        {
            motor_mode = 0;
        }
    }
}

void Line_PID_control(uint8_t data) // 循迹环控制
{
    int line_pid_output = 0;
    line_pid_output = pid_calculate_incremental(&pid_line, track_get_line_position_error(data));
    line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

    pid_set_target(&pid_speed_left, basic_speed + line_pid_output);
    pid_set_target(&pid_speed_right, basic_speed - line_pid_output);
}

void Angle_PID_control(float yaw) // 角度环控制
{
    int angle_pid_output = 0;
    angle_pid_output = pid_calculate_incremental(&pid_angle, yaw);
    angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);
    pid_set_target(&pid_speed_left, basic_speed + 0.8 * angle_pid_output);
    pid_set_target(&pid_speed_right, basic_speed - 0.8 * angle_pid_output);
}

void motor_mode1(void)
{
}

/**
 * @brief       电机任务
 * @param       arg: 传入的参数
 * @retval      无
 */
static void app_motor_task(void *arg)
{
    TickType_t tick;
    static float angle_last = 0;
    static float angle_now = 100;
    static uint8_t cnt = 0;
    static uint16_t temp = 70;
    tick = xTaskGetTickCount();
#define DELAY_TIME 10
    motor_pid_init();
    while (is_app_motor_task_create)
    {
        vTaskDelayUntil(&tick, DELAY_TIME);

        if (motor_mode == 0)
        {
            motor_set_speed(MOTOR_L, 0);
            motor_set_speed(MOTOR_R, 0);
            continue;
        }

        jyxx_up_data();
        track_read_sensors();

        uint8_t track_data = track_get_sensor_data();
        Line_PID_control(track_data);

        float *angle = jyxx_get_angle();

        uint16_t juli = motor_get_distance(MOTOR_L);

        // if (juli > 80)
        //     basic_speed = 25;
        // else
        //     basic_speed = 60;

//        if (juli > 50)
//            temp = 25;
        // else
        //     temp = 70;

        if (temp > basic_speed)
            basic_speed += 0.5;
        else if (temp < basic_speed)
            basic_speed -= 0.5;

        debug_printf("juli:%d\r\n", juli);
        debug_printf("basic_speed:%.2f\r\n", basic_speed);

        if (angle[2] > 85)
        {
            temp = 80;
            extern void jy60_send_command(uint8_t cmd);
            jy60_send_command(0x52);
            motor_reset_distance(MOTOR_L, 20);
            angle_last = angle[2];
        }
        angle_now = angle[2];
        if (angle_last > 85 && angle_now < 10)
        {
            angle_last = angle_now;
            cnt++;
            if (cnt == 16)
            {
                motor_mode = 0;
            }
        }

        float left_encoder_speed = motor_get_speed(MOTOR_L, DELAY_TIME);
        float right_encoder_speed = motor_get_speed(MOTOR_R, DELAY_TIME);

        encoder_int_set_value(MOTOR_L, 0);
        encoder_int_set_value(MOTOR_R, 0);

        filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * left_encoder_speed + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
        filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * right_encoder_speed + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;

        float output_speed_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
        float output_speed_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);

        output_speed_left = pid_constrain(output_speed_left, pid_params_speed_left.out_min, pid_params_speed_left.out_max);
        output_speed_right = pid_constrain(output_speed_right, pid_params_speed_right.out_min, pid_params_speed_right.out_max);

        debug_printf(",left_encoder_speed=%.2f,output_speed_left=%.2f\r\n", left_encoder_speed, output_speed_left / 10.0f);
        debug_printf(",right_encoder_speed=%.2f,output_speed_right=%.2f\r\n", right_encoder_speed, output_speed_right / 10.0f);

        motor_set_speed(MOTOR_L, output_speed_left);
        motor_set_speed(MOTOR_R, output_speed_right);
    }
}

/**
 * @brief       电机任务启动
 * @param       无
 * @retval      1:失败 0:成功
 */
uint8_t app_motor_task_start(void)
{
    if (is_app_motor_task_create != 0)
    {
        return 1;
    }

    if (xTaskCreate(app_motor_task, "app_motor_task", 1024, NULL, 3, NULL) != pdPASS)
    {
        return 1;
    }
    is_app_motor_task_create = 1;
    key_callback_register(app_motor_key_handle);
    return 0;
}

/**
 * @brief       电机任务停止
 * @param       无
 * @retval      无
 */
void app_motor_task_stop(void)
{
    is_app_motor_task_create = 0;
    key_callback_unregister(app_motor_key_handle);
}
