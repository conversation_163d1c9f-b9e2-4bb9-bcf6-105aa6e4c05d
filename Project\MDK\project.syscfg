/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const Board  = scripting.addModule("/ti/driverlib/Board");
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();
const UART2  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider2       = system.clockTree["HFCLK4MFPCLKDIV"];
divider2.divideValue = 10;

const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 1;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const gate8  = system.clockTree["MFPCLKGATE"];
gate8.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux10       = system.clockTree["MFPCLKMUX"];
mux10.inputSelect = "MFPCLKMUX_HFCLK";

const pinFunction4                                      = system.clockTree["HFXT"];
pinFunction4.enable                                     = true;
pinFunction4.inputFreq                                  = 40;
pinFunction4.HFCLKMonitor                               = true;
pinFunction4.HFXTStartup                                = 100;
pinFunction4.peripheral.hfxInPin.$assignAllowConflicts  = "PA5";
pinFunction4.peripheral.hfxOutPin.$assignAllowConflicts = "PA6";

ADC121.repeatMode                        = true;
ADC121.adcMem0_name                      = "ADC_KEY";
ADC121.configureDMA                      = true;
ADC121.sampCnt                           = 1;
ADC121.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC121.adcMem1chansel                    = "DL_ADC12_INPUT_CHAN_1";
ADC121.adcMem2chansel                    = "DL_ADC12_INPUT_CHAN_2";
ADC121.adcMem1_name                      = "ADC_KEY1";
ADC121.adcMem2_name                      = "ADC_KEY2";
ADC121.$name                             = "ADC";
ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_32";
ADC121.peripheral.$assign                = "ADC1";
ADC121.DMA_CHANNEL.$name                 = "DMA_CH0";
ADC121.DMA_CHANNEL.addressMode           = "f2b";
ADC121.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.configureTransferSize = true;
ADC121.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric2";


GPIO1.$name                          = "LED";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].$name        = "LED1";
GPIO1.associatedPins[0].assignedPin  = "14";
GPIO1.associatedPins[1].$name        = "LED2";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].assignedPin  = "18";
GPIO1.associatedPins[2].$name        = "LED3";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "22";
GPIO1.associatedPins[3].$name        = "LED4";
GPIO1.associatedPins[3].assignedPort = "PORTA";
GPIO1.associatedPins[3].assignedPin  = "7";

GPIO2.$name                              = "SOFT_I2C0";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name            = "SDA";
GPIO2.associatedPins[0].assignedPort     = "PORTB";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPin      = "0";
GPIO2.associatedPins[1].$name            = "SCL";
GPIO2.associatedPins[1].assignedPort     = "PORTB";
GPIO2.associatedPins[1].assignedPin      = "1";

GPIO3.$name                          = "TB6612";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name        = "AIN1";
GPIO3.associatedPins[0].assignedPort = "PORTB";
GPIO3.associatedPins[0].assignedPin  = "9";
GPIO3.associatedPins[1].$name        = "AIN2";
GPIO3.associatedPins[1].assignedPort = "PORTB";
GPIO3.associatedPins[1].assignedPin  = "8";
GPIO3.associatedPins[2].$name        = "BIN1";
GPIO3.associatedPins[2].assignedPort = "PORTB";
GPIO3.associatedPins[2].assignedPin  = "7";
GPIO3.associatedPins[3].$name        = "BIN2";
GPIO3.associatedPins[3].assignedPort = "PORTB";
GPIO3.associatedPins[3].assignedPin  = "6";

GPIO4.$name                               = "ENCODER";
GPIO4.associatedPins.create(4);
GPIO4.associatedPins[0].$name             = "A1";
GPIO4.associatedPins[0].direction         = "INPUT";
GPIO4.associatedPins[0].internalResistor  = "PULL_UP";
GPIO4.associatedPins[0].interruptEn       = true;
GPIO4.associatedPins[0].interruptPriority = "0";
GPIO4.associatedPins[0].polarity          = "FALL";
GPIO4.associatedPins[0].assignedPort      = "PORTB";
GPIO4.associatedPins[0].assignedPin       = "15";
GPIO4.associatedPins[0].pin.$assign       = "PB15";
GPIO4.associatedPins[1].$name             = "B1";
GPIO4.associatedPins[1].direction         = "INPUT";
GPIO4.associatedPins[1].internalResistor  = "PULL_UP";
GPIO4.associatedPins[1].assignedPort      = "PORTB";
GPIO4.associatedPins[1].assignedPin       = "16";
GPIO4.associatedPins[2].$name             = "A2";
GPIO4.associatedPins[2].assignedPort      = "PORTB";
GPIO4.associatedPins[2].assignedPin       = "11";
GPIO4.associatedPins[2].direction         = "INPUT";
GPIO4.associatedPins[2].internalResistor  = "PULL_UP";
GPIO4.associatedPins[2].interruptEn       = true;
GPIO4.associatedPins[2].interruptPriority = "0";
GPIO4.associatedPins[2].polarity          = "FALL";
GPIO4.associatedPins[3].$name             = "B2";
GPIO4.associatedPins[3].assignedPort      = "PORTB";
GPIO4.associatedPins[3].assignedPin       = "10";
GPIO4.associatedPins[3].internalResistor  = "PULL_UP";
GPIO4.associatedPins[3].direction         = "INPUT";

GPIO5.$name                              = "TRACK";
GPIO5.associatedPins.create(8);
GPIO5.associatedPins[0].$name            = "A";
GPIO5.associatedPins[0].direction        = "INPUT";
GPIO5.associatedPins[0].internalResistor = "PULL_UP";
GPIO5.associatedPins[0].assignedPort     = "PORTA";
GPIO5.associatedPins[0].assignedPin      = "8";
GPIO5.associatedPins[1].$name            = "B";
GPIO5.associatedPins[1].direction        = "INPUT";
GPIO5.associatedPins[1].assignedPort     = "PORTA";
GPIO5.associatedPins[1].assignedPin      = "9";
GPIO5.associatedPins[1].internalResistor = "PULL_UP";
GPIO5.associatedPins[2].$name            = "C";
GPIO5.associatedPins[2].direction        = "INPUT";
GPIO5.associatedPins[2].assignedPort     = "PORTB";
GPIO5.associatedPins[2].assignedPin      = "12";
GPIO5.associatedPins[2].internalResistor = "PULL_UP";
GPIO5.associatedPins[3].$name            = "D";
GPIO5.associatedPins[3].direction        = "INPUT";
GPIO5.associatedPins[3].assignedPort     = "PORTB";
GPIO5.associatedPins[3].assignedPin      = "26";
GPIO5.associatedPins[3].internalResistor = "PULL_UP";
GPIO5.associatedPins[4].$name            = "E";
GPIO5.associatedPins[4].assignedPort     = "PORTB";
GPIO5.associatedPins[4].assignedPin      = "4";
GPIO5.associatedPins[4].internalResistor = "PULL_UP";
GPIO5.associatedPins[4].direction        = "INPUT";
GPIO5.associatedPins[5].$name            = "F";
GPIO5.associatedPins[5].direction        = "INPUT";
GPIO5.associatedPins[5].assignedPort     = "PORTB";
GPIO5.associatedPins[5].assignedPin      = "5";
GPIO5.associatedPins[5].internalResistor = "PULL_UP";
GPIO5.associatedPins[6].$name            = "G";
GPIO5.associatedPins[6].assignedPort     = "PORTB";
GPIO5.associatedPins[6].assignedPin      = "13";
GPIO5.associatedPins[6].direction        = "INPUT";
GPIO5.associatedPins[6].internalResistor = "PULL_UP";
GPIO5.associatedPins[7].$name            = "H";
GPIO5.associatedPins[7].assignedPort     = "PORTB";
GPIO5.associatedPins[7].assignedPin      = "27";
GPIO5.associatedPins[7].direction        = "INPUT";
GPIO5.associatedPins[7].internalResistor = "PULL_UP";

PWM1.$name                              = "PWM_MOTOR_A";
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.timerStartTimer                    = true;
PWM1.clockDivider                       = 8;
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 100;
PWM1.PWM_CHANNEL_0.initVal              = "HIGH";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.initVal              = "HIGH";
PWM1.PWM_CHANNEL_1.dutyCycle            = 100;
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";

SYSCTL.HFCLKSource  = "HFXT";
SYSCTL.MFPCLKSource = "HFCLK";
SYSCTL.clockTreeEn  = true;

TIMER1.$name              = "TIMER_FUNS";
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "0.01";
TIMER1.timerClkPrescale   = 250;
TIMER1.timerClkDiv        = 8;
TIMER1.peripheral.$assign = "TIMG8";

UART1.$name                            = "UART_DEBUG";
UART1.enabledInterrupts                = ["RX"];
UART1.targetBaudRate                   = 1000000;
UART1.interruptPriority                = "3";
UART1.peripheral.$assign               = "UART0";
UART1.peripheral.rxPin.$assign         = "PA11";
UART1.peripheral.txPin.$assign         = "PA10";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                            = "UART_JYXX";
UART2.rxFifoThreshold                  = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART2.enableDMARX                      = false;
UART2.enableDMATX                      = false;
UART2.enabledInterrupts                = ["RX"];
UART2.interruptPriority                = "0";
UART2.uartClkSrc                       = "BUSCLK";
UART2.ovsRate                          = "16";
UART2.enableFIFO                       = false;
UART2.peripheral.rxPin.$assign         = "PA25";
UART2.peripheral.txPin.$assign         = "PA26";
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.internalResistor     = "PULL_UP";
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.internalResistor     = "PULL_UP";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution       = "SYSCTL";
ADC121.peripheral.adcPin0.$suggestSolution     = "PA15";
ADC121.DMA_CHANNEL.peripheral.$suggestSolution = "DMA_CH0";
Board.peripheral.$suggestSolution              = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution     = "PA20";
Board.peripheral.swdioPin.$suggestSolution     = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution   = "PB14";
GPIO1.associatedPins[1].pin.$suggestSolution   = "PA18";
GPIO1.associatedPins[2].pin.$suggestSolution   = "PB22";
GPIO1.associatedPins[3].pin.$suggestSolution   = "PA7";
GPIO2.associatedPins[0].pin.$suggestSolution   = "PB0";
GPIO2.associatedPins[1].pin.$suggestSolution   = "PB1";
GPIO3.associatedPins[0].pin.$suggestSolution   = "PB9";
GPIO3.associatedPins[1].pin.$suggestSolution   = "PB8";
GPIO3.associatedPins[2].pin.$suggestSolution   = "PB7";
GPIO3.associatedPins[3].pin.$suggestSolution   = "PB6";
GPIO4.associatedPins[1].pin.$suggestSolution   = "PB16";
GPIO4.associatedPins[2].pin.$suggestSolution   = "PB11";
GPIO4.associatedPins[3].pin.$suggestSolution   = "PB10";
GPIO5.associatedPins[0].pin.$suggestSolution   = "PA8";
GPIO5.associatedPins[1].pin.$suggestSolution   = "PA9";
GPIO5.associatedPins[2].pin.$suggestSolution   = "PB12";
GPIO5.associatedPins[3].pin.$suggestSolution   = "PB26";
GPIO5.associatedPins[4].pin.$suggestSolution   = "PB4";
GPIO5.associatedPins[5].pin.$suggestSolution   = "PB5";
GPIO5.associatedPins[6].pin.$suggestSolution   = "PB13";
GPIO5.associatedPins[7].pin.$suggestSolution   = "PB27";
UART2.peripheral.$suggestSolution              = "UART3";
