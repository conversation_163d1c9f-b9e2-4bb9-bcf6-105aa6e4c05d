#include "./BSP/SYSTEM/system.h"
#include "./BSP/TB6612/tb6612.h"

const uint32_t tb6612_control_pins[][2] = {CFG_MOTOR_DEIVER_CONTROL_PIN};
const GPIO_Regs *tb6612_control_gpio_regs[][2] = {CFG_MOTOR_DEIVER_CONTROL_PORT};
const GPTIMER_Regs *tb6612_pwm_tim_id[] = {CFG_MOTOR_DRIVER_PWM_TIMER};
const uint8_t tb6612_pwm_tim_ch[] = {CFG_MOTOR_DRIVER_PWM_TIMER_CH};

/**
 * @brief       TB6612电机驱动初始化函数
 * @note        该函数会初始化TB6612控制引脚和PWM引脚
 * @retval      无
 */
void tb6612_init(void)
{

}

/**
 * @brief       设置TB6612电机驱动方向
 * @param       motor_id: 电机编号
 * @param       direction: 电机方向, 0:停止 1:正转, 2:反转
 * @retval      无
 */
void tb6612_set_direction(uint8_t motor_id, uint8_t direction)
{
    if (direction == CFG_MOTOR_DIRECTION_FORWARD) // 正转
    {
        DL_GPIO_setPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][0], tb6612_control_pins[motor_id][0]);
        DL_GPIO_clearPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][1], tb6612_control_pins[motor_id][1]);
    }
    if (direction == CFG_MOTOR_DIRECTION_BACKWARD) // 反转
    {
        DL_GPIO_clearPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][0], tb6612_control_pins[motor_id][0]);
        DL_GPIO_setPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][1], tb6612_control_pins[motor_id][1]);
    }
    if (direction == CFG_MOTOR_DIRECTION_STOP) // 停止
    {
        DL_GPIO_clearPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][0], tb6612_control_pins[motor_id][0]);
        DL_GPIO_clearPins((GPIO_Regs *)tb6612_control_gpio_regs[motor_id][1], tb6612_control_pins[motor_id][1]);
    }
}

/**
 * @brief       设置TB6612电机驱动PWM值
 * @param       motor_id: 电机编号
 * @param       value: 占空比
 * @retval      无
 */
void tb6612_set_pwm_value(uint8_t motor_id, uint16_t value)
{
    DL_TimerG_setCaptureCompareValue((GPTIMER_Regs *)tb6612_pwm_tim_id[motor_id], value, tb6612_pwm_tim_ch[motor_id]);
}
